(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))a(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&a(l)}).observe(document,{childList:!0,subtree:!0});function t(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(s){if(s.ep)return;s.ep=!0;const i=t(s);fetch(s.href,i)}})();async function o(n,e={},t){return window.__TAURI_INTERNALS__.invoke(n,e,t)}class b{container;emails=[];loading=!1;currentPage=1;pageSize=20;displayLimit=15;totalCount=0;selectedEmailId=null;onEmailSelect=null;constructor(e){this.container=e,this.render(),this.loadEmails()}setOnEmailSelect(e){this.onEmailSelect=e}async loadEmails(){this.setLoading(!0);try{const e=await o("get_processed_emails",{page:this.currentPage,pageSize:this.pageSize});this.emails=e.emails,this.totalCount=e.total_count,this.currentPage=e.page,this.pageSize=e.page_size,this.renderEmailList()}catch(e){this.renderError(`Failed to load emails: ${e}`)}finally{this.setLoading(!1)}}setLoading(e){this.loading=e,this.renderLoadingState()}render(){this.container.innerHTML=`
      <div class="email-list-container">
        <div class="email-list-header">
          <h2>Processed Emails</h2>
          <div class="email-list-controls">
            <button id="refresh-emails" class="btn btn-secondary">
              <span class="icon">🔄</span>
              Refresh
            </button>
          </div>
        </div>
        <div class="email-list-content">
          <div id="email-list-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading emails...</p>
          </div>
          <div id="email-list-error" class="error-state" style="display: none;"></div>
          <div id="email-list-items" class="email-list-items"></div>
          <div id="email-list-pagination" class="pagination-container"></div>
        </div>
      </div>
    `,this.container.querySelector("#refresh-emails")?.addEventListener("click",()=>this.loadEmails())}renderLoadingState(){const e=this.container.querySelector("#email-list-loading"),t=this.container.querySelector("#email-list-items");this.loading?(e.style.display="flex",t.style.display="none"):(e.style.display="none",t.style.display="block")}renderError(e){const t=this.container.querySelector("#email-list-error"),a=this.container.querySelector("#email-list-items");t.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${e}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `,t.style.display="block",a.style.display="none"}renderEmailList(){const e=this.container.querySelector("#email-list-items"),t=this.container.querySelector("#email-list-error");if(t.style.display="none",this.emails.length===0){e.innerHTML=`
        <div class="empty-state">
          <span class="icon">📧</span>
          <h3>No emails found</h3>
          <p>Process some email files to see them here.</p>
        </div>
      `;return}const a=this.emails.slice(0,this.displayLimit),s=a.map(i=>this.renderEmailItem(i)).join("");e.innerHTML=s,e.querySelectorAll(".email-item").forEach((i,l)=>{i.addEventListener("click",()=>{this.selectEmail(a[l])}),i.addEventListener("dblclick",()=>{this.showEmailModal(a[l])})}),this.renderPagination()}renderEmailItem(e){const t=this.selectedEmailId===e.id,a=e.sent_date?new Date(e.sent_date).toLocaleDateString():"Unknown",s=e.from_address||"Unknown sender",i=e.subject||"(No subject)",l=e.preview||"No preview available";return`
      <div class="email-item ${t?"selected":""}" data-email-id="${e.id}">
        <div class="email-item-header">
          <div class="email-subject">${this.escapeHtml(i)}</div>
          <div class="email-date">${a}</div>
        </div>
        <div class="email-from">${this.escapeHtml(s)}</div>
        <div class="email-preview">${this.escapeHtml(l)}</div>
        <div class="email-recipients">
          To: ${e.to_addresses.map(r=>this.escapeHtml(r)).join(", ")}
        </div>
      </div>
    `}renderPagination(){const e=this.container.querySelector("#email-list-pagination");if(this.totalCount<=this.pageSize){e.style.display="none";return}const t=Math.ceil(this.totalCount/this.pageSize),a=(this.currentPage-1)*this.pageSize+1,s=Math.min(this.currentPage*this.pageSize,this.totalCount),i=Math.min(this.emails.length,this.displayLimit);e.innerHTML=`
      <div class="pagination">
        <button class="btn btn-secondary" ${this.currentPage<=1?"disabled":""}
                onclick="this.previousPage()">Previous</button>
        <span class="pagination-info">
          Showing ${i} of ${this.emails.length} emails (${a}-${s} of ${this.totalCount} total)
        </span>
        <button class="btn btn-secondary" ${this.currentPage>=t?"disabled":""}
                onclick="this.nextPage()">Next</button>
      </div>
    `,e.style.display="flex"}selectEmail(e){this.selectedEmailId=e.id,this.renderEmailList(),this.onEmailSelect&&this.onEmailSelect(e)}previousPage(){this.currentPage>1&&(this.currentPage--,this.loadEmails())}nextPage(){const e=Math.ceil(this.totalCount/this.pageSize);this.currentPage<e&&(this.currentPage++,this.loadEmails())}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}refresh(){this.loadEmails()}getSelectedEmail(){return this.emails.find(e=>e.id===this.selectedEmailId)||null}async showEmailModal(e){try{const t=await o("get_email_details",{emailId:e.id});this.createEmailModal(t)}catch(t){console.error("Failed to load email details for modal:",t),alert(`Failed to load email details: ${t}`)}}createEmailModal(e){const t=document.querySelector(".email-modal");t&&t.remove();const a=document.createElement("div");a.className="email-modal";const s=e.sent_date?new Date(e.sent_date).toLocaleString():"Unknown",i=e.subject||"(No subject)",l=e.from_address||"Unknown sender",r=e.plain_text_content||"No content available",p=e.to_addresses.join(", ")||"Unknown recipients";a.innerHTML=`
      <div class="email-modal-content">
        <div class="email-modal-header">
          <h2 class="email-modal-title">${this.escapeHtml(i)}</h2>
          <button class="email-modal-close" aria-label="Close modal">&times;</button>
        </div>
        <div class="email-modal-body">
          <div class="email-modal-metadata">
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">From:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(l)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">To:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(p)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">Date:</span>
              <span class="email-modal-metadata-value">${s}</span>
            </div>
          </div>
          <div class="email-modal-content-section">
            <pre class="email-modal-text">${this.escapeHtml(r)}</pre>
          </div>
        </div>
      </div>
    `,document.body.appendChild(a),a.querySelector(".email-modal-close")?.addEventListener("click",()=>this.closeEmailModal()),a.addEventListener("click",m=>{m.target===a&&this.closeEmailModal()});const u=m=>{m.key==="Escape"&&(this.closeEmailModal(),document.removeEventListener("keydown",u))};document.addEventListener("keydown",u),setTimeout(()=>{a.classList.add("show")},10)}closeEmailModal(){const e=document.querySelector(".email-modal");e&&(e.classList.remove("show"),setTimeout(()=>{e.remove()},150))}}class g{container;email=null;loading=!1;onGenerateDraft=null;constructor(e){this.container=e,this.render()}setOnGenerateDraft(e){this.onGenerateDraft=e}async loadEmail(e){this.setLoading(!0);try{const t=await o("get_email_details",{emailId:e.id});this.email=t,this.renderEmailDetail()}catch(t){this.renderError(`Failed to load email details: ${t}`)}finally{this.setLoading(!1)}}setLoading(e){this.loading=e,this.renderLoadingState()}render(){this.container.innerHTML=`
      <div class="email-detail-container">
        <div class="email-detail-header">
          <h2>Email Details</h2>
          <div class="email-detail-actions">
            <button id="generate-draft-btn" class="btn btn-primary" style="display: none;">
              <span class="icon">✨</span>
              Generate Draft Reply
            </button>
          </div>
        </div>
        <div class="email-detail-content">
          <div id="email-detail-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading email details...</p>
          </div>
          <div id="email-detail-error" class="error-state" style="display: none;"></div>
          <div id="email-detail-info" class="email-detail-info" style="display: none;"></div>
        </div>
      </div>
    `,this.container.querySelector("#generate-draft-btn")?.addEventListener("click",()=>{this.email&&this.onGenerateDraft&&this.onGenerateDraft(this.email.id)})}renderLoadingState(){const e=this.container.querySelector("#email-detail-loading"),t=this.container.querySelector("#email-detail-info"),a=this.container.querySelector("#email-detail-error");this.loading?(e.style.display="flex",t.style.display="none",a.style.display="none"):e.style.display="none"}renderError(e){const t=this.container.querySelector("#email-detail-error"),a=this.container.querySelector("#email-detail-info"),s=this.container.querySelector("#generate-draft-btn");t.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${e}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `,t.style.display="block",a.style.display="none",s.style.display="none"}renderEmailDetail(){if(!this.email)return;const e=this.container.querySelector("#email-detail-info"),t=this.container.querySelector("#email-detail-error"),a=this.container.querySelector("#generate-draft-btn");t.style.display="none";const s=this.email.sent_date?new Date(this.email.sent_date).toLocaleString():"Unknown",i=new Date(this.email.created_at).toLocaleString(),l=this.email.subject||"(No subject)",r=this.email.from_address||"Unknown sender",p=this.email.plain_text_content||"No content available";e.innerHTML=`
      <div class="email-metadata">
        <div class="metadata-row">
          <label>Subject:</label>
          <span class="email-subject">${this.escapeHtml(l)}</span>
        </div>
        <div class="metadata-row">
          <label>From:</label>
          <span class="email-from">${this.escapeHtml(r)}</span>
        </div>
        <div class="metadata-row">
          <label>To:</label>
          <span class="email-to">${this.email.to_addresses.map(h=>this.escapeHtml(h)).join(", ")}</span>
        </div>
        <div class="metadata-row">
          <label>Sent:</label>
          <span class="email-sent-date">${s}</span>
        </div>
        <div class="metadata-row">
          <label>Processed:</label>
          <span class="email-created-date">${i}</span>
        </div>
        ${this.email.file_path?`
        <div class="metadata-row">
          <label>Source File:</label>
          <span class="email-file-path">${this.escapeHtml(this.email.file_path)}</span>
        </div>
        `:""}
      </div>
      <div class="email-content">
        <h3>Email Content</h3>
        <div class="email-body">
          <pre class="email-text">${this.escapeHtml(p)}</pre>
        </div>
      </div>
    `,e.style.display="block",a.style.display="inline-flex"}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}clear(){this.email=null;const e=this.container.querySelector("#email-detail-info"),t=this.container.querySelector("#email-detail-error"),a=this.container.querySelector("#generate-draft-btn");e.style.display="none",t.style.display="none",a.style.display="none",e.innerHTML=`
      <div class="empty-state">
        <span class="icon">📧</span>
        <h3>Select an email</h3>
        <p>Choose an email from the list to view its details.</p>
      </div>
    `,e.style.display="block"}getCurrentEmail(){return this.email}}class E{container;draftContent="";loading=!1;constructor(e){this.container=e,this.render()}render(){this.container.innerHTML=`
      <div class="draft-display-container">
        <div class="draft-display-header">
          <h2>Generated Draft Reply</h2>
          <div class="draft-display-actions">
            <button id="copy-draft-btn" class="btn btn-secondary" style="display: none;">
              <span class="icon">📋</span>
              Copy to Clipboard
            </button>
            <button id="clear-draft-btn" class="btn btn-outline" style="display: none;">
              <span class="icon">🗑️</span>
              Clear
            </button>
          </div>
        </div>
        <div class="draft-display-content">
          <div id="draft-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Generating draft reply...</p>
          </div>
          <div id="draft-error" class="error-state" style="display: none;"></div>
          <div id="draft-content" class="draft-content-area">
            <div class="empty-state">
              <span class="icon">✨</span>
              <h3>No draft generated</h3>
              <p>Select an email and click "Generate Draft Reply" to create an AI-powered response.</p>
            </div>
          </div>
        </div>
      </div>
    `;const e=this.container.querySelector("#copy-draft-btn"),t=this.container.querySelector("#clear-draft-btn");e?.addEventListener("click",()=>this.copyToClipboard()),t?.addEventListener("click",()=>this.clearDraft())}setLoading(e){this.loading=e,this.renderLoadingState()}renderLoadingState(){const e=this.container.querySelector("#draft-loading"),t=this.container.querySelector("#draft-content"),a=this.container.querySelector("#copy-draft-btn"),s=this.container.querySelector("#clear-draft-btn");this.loading?(e.style.display="flex",t.style.display="none",a.style.display="none",s.style.display="none"):(e.style.display="none",t.style.display="block")}renderError(e){const t=this.container.querySelector("#draft-error"),a=this.container.querySelector("#draft-content"),s=this.container.querySelector("#copy-draft-btn"),i=this.container.querySelector("#clear-draft-btn");t.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${e}</p>
        <button class="btn btn-primary" onclick="this.retryGeneration()">Retry</button>
      </div>
    `,t.style.display="block",a.style.display="none",s.style.display="none",i.style.display="none"}displayDraft(e,t){this.draftContent=e;const a=this.container.querySelector("#draft-content"),s=this.container.querySelector("#draft-error"),i=this.container.querySelector("#copy-draft-btn"),l=this.container.querySelector("#clear-draft-btn");s.style.display="none";const r=t?.contextEmailsCount?`Based on ${t.contextEmailsCount} similar email${t.contextEmailsCount!==1?"s":""}`:"Generated using AI";a.innerHTML=`
      <div class="draft-text-container">
        <div class="draft-metadata">
          <span class="draft-timestamp">Generated: ${new Date().toLocaleString()}</span>
          <span class="draft-length">${e.length} characters</span>
          <span class="draft-context">${r}</span>
        </div>
        <div class="draft-text-area">
          <textarea
            class="draft-textarea"
            readonly
            placeholder="Generated draft will appear here..."
          >${this.escapeHtml(e)}</textarea>
        </div>
        <div class="draft-actions-bottom">
          <button class="btn btn-outline" onclick="this.editDraft()">
            <span class="icon">✏️</span>
            Edit Draft
          </button>
          <button class="btn btn-secondary" onclick="this.regenerateDraft()">
            <span class="icon">🔄</span>
            Regenerate
          </button>
        </div>
      </div>
    `,a.style.display="block",i.style.display="inline-flex",l.style.display="inline-flex"}displayError(e){const t=this.container.querySelector("#draft-content"),a=this.container.querySelector("#draft-error"),s=this.container.querySelector("#copy-draft-btn"),i=this.container.querySelector("#clear-draft-btn");a.innerHTML=`
      <div class="error-message">
        <span class="icon">⚠️</span>
        <h3>Draft Generation Failed</h3>
        <p>${this.escapeHtml(e)}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `,a.style.display="block",t.style.display="none",s.style.display="none",i.style.display="none"}displayPlaceholder(){this.draftContent="";const e=this.container.querySelector("#draft-content"),t=this.container.querySelector("#draft-error"),a=this.container.querySelector("#copy-draft-btn"),s=this.container.querySelector("#clear-draft-btn");t.style.display="none",e.innerHTML=`
      <div class="placeholder-state">
        <span class="icon">🚧</span>
        <h3>Draft Generation Coming Soon</h3>
        <p>The RAG service for AI-powered draft generation is currently being developed.</p>
        <p>For now, you can view email details and prepare for draft generation functionality.</p>
        <div class="placeholder-draft">
          <h4>Sample Draft Preview:</h4>
          <div class="sample-draft">
            <p>Dear [Sender Name],</p>
            <p>Thank you for your email regarding [Subject]. I have reviewed your message and would like to respond as follows:</p>
            <p>[AI-generated response content will appear here]</p>
            <p>Please let me know if you need any additional information.</p>
            <p>Best regards,<br>[Your Name]</p>
          </div>
        </div>
      </div>
    `,e.style.display="block",a.style.display="none",s.style.display="none"}async copyToClipboard(){if(this.draftContent)try{await navigator.clipboard.writeText(this.draftContent),this.showCopySuccess()}catch(e){this.showCopyError(`Failed to copy to clipboard: ${e}`)}}showCopySuccess(){const e=this.container.querySelector("#copy-draft-btn"),t=e.innerHTML;e.innerHTML='<span class="icon">✅</span> Copied!',e.disabled=!0,setTimeout(()=>{e.innerHTML=t,e.disabled=!1},2e3)}showCopyError(e){console.error(e),alert(e)}clearDraft(){this.draftContent="";const e=this.container.querySelector("#draft-content"),t=this.container.querySelector("#copy-draft-btn"),a=this.container.querySelector("#clear-draft-btn");e.innerHTML=`
      <div class="empty-state">
        <span class="icon">✨</span>
        <h3>No draft generated</h3>
        <p>Select an email and click "Generate Draft Reply" to create an AI-powered response.</p>
      </div>
    `,t.style.display="none",a.style.display="none"}editDraft(){const e=this.container.querySelector(".draft-textarea");e&&(e.readOnly=!1,e.focus(),e.addEventListener("input",()=>{this.draftContent=e.value}))}regenerateDraft(){this.displayPlaceholder()}retryGeneration(){this.displayPlaceholder()}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}getDraftContent(){return this.draftContent}hasDraft(){return this.draftContent.length>0}}async function S(n={}){return typeof n=="object"&&Object.freeze(n),await o("plugin:dialog|open",{options:n})}document.querySelector("#app").innerHTML=`
  <div class="app-container">
    <header class="app-header">
      <h1>AI-Assisted Email Response System</h1>
      <div class="service-status">
        <div id="service-indicators" class="service-indicators">
          <div class="service-indicator" id="ingestion-status">
            <span class="indicator-dot"></span>
            <span class="indicator-label">Ingestion Service</span>
          </div>
          <div class="service-indicator" id="rag-status">
            <span class="indicator-dot"></span>
            <span class="indicator-label">RAG Service</span>
          </div>
        </div>
        <button id="file-upload-btn" class="btn btn-primary">
          <span class="icon">📁</span>
          Process Email Files
        </button>
      </div>
    </header>

    <main class="app-main">
      <div class="app-layout">
        <div class="sidebar">
          <div id="email-list-container" class="email-list-section"></div>
        </div>
        
        <div class="main-content">
          <div class="content-top">
            <div id="email-detail-container" class="email-detail-section"></div>
          </div>
          
          <div class="content-bottom">
            <div id="draft-display-container" class="draft-display-section"></div>
          </div>
        </div>
      </div>
    </main>
  </div>
`;const L=document.querySelector("#email-list-container"),w=document.querySelector("#email-detail-container"),q=document.querySelector("#draft-display-container"),y=new b(L),d=new g(w),c=new E(q);y.setOnEmailSelect(n=>{d.loadEmail(n),c.displayPlaceholder()});d.setOnGenerateDraft(async n=>{try{c.setLoading(!0);const e=d.email;if(!e)throw new Error("No email selected");const t=e.subject||"(No subject)",a=e.from_address||"Unknown sender",s=e.plain_text_content||"";if(!s.trim())throw new Error("Email content is empty");const i=await o("generate_draft",{subject:t,sender:a,content:s});c.displayDraft(i.draft,{contextEmailsCount:i.context_emails_count,similarEmails:i.similar_emails,metadata:i.metadata})}catch(e){console.error("Draft generation failed:",e),c.displayError(`Failed to generate draft: ${e}`)}finally{c.setLoading(!1)}});d.clear();c.displayPlaceholder();const $=document.querySelector("#file-upload-btn");$?.addEventListener("click",async()=>{try{const n=await S({multiple:!0,filters:[{name:"Email Files",extensions:["eml","mbox"]}]});if(n&&Array.isArray(n)){for(const e of n)await f(e);y.refresh()}else n&&(await f(n),y.refresh())}catch(n){console.error("File selection failed:",n),alert(`File selection failed: ${n}`)}});async function f(n){try{const e=n.toLowerCase().endsWith(".mbox")?"mbox":"eml",t=await o("process_email_file",{request:{file_path:n,file_type:e}});console.log("Processing result:",t),alert(`Successfully processed ${n}`)}catch(e){console.error("Processing failed:",e),alert(`Failed to process ${n}: ${e}`)}}async function D(){try{const n=await o("check_service_health",{serviceUrl:"http://localhost:8080"});v("ingestion-status",n),v("rag-status",{service:"http://localhost:8001",status:"unavailable",message:"RAG service not implemented yet"})}catch(n){console.error("Service health check failed:",n)}}function v(n,e){const t=document.querySelector(`#${n}`);if(!t)return;const a=t.querySelector(".indicator-dot");switch(a.classList.remove("status-healthy","status-unhealthy","status-error","status-unavailable"),e.status){case"healthy":a.classList.add("status-healthy");break;case"unhealthy":a.classList.add("status-unhealthy");break;case"unavailable":a.classList.add("status-unavailable");break;default:a.classList.add("status-error")}t.title=`${e.service}: ${e.message}`}D();
