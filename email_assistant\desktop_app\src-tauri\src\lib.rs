use serde::{Deserialize, Serialize};


#[derive(Debug, Serialize, Deserialize)]
pub struct ServiceHealthResponse {
    pub service: String,
    pub status: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingRequest {
    pub file_path: String,
    pub file_type: String, // "eml" or "mbox"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingResponse {
    pub success: bool,
    pub message: String,
    pub processed_count: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailSummary {
    pub id: String,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<String>,
    pub created_at: String,
    pub preview: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailListResponse {
    pub emails: Vec<EmailSummary>,
    pub total_count: u64,
    pub page: u64,
    pub page_size: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailDetailResponse {
    pub id: String,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<String>,
    pub created_at: String,
    pub plain_text_content: Option<String>,
    pub file_path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DraftRequest {
    pub subject: String,
    pub sender: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DraftResponse {
    pub draft: String,
    pub context_emails_count: i32,
    pub similar_emails: Vec<serde_json::Value>,
    pub metadata: serde_json::Value,
}

// Tauri command to check service health
#[tauri::command]
async fn check_service_health(service_url: String) -> Result<ServiceHealthResponse, String> {
    let client = reqwest::Client::new();

    match client.get(&format!("{}/health", service_url)).send().await {
        Ok(response) => {
            if response.status().is_success() {
                Ok(ServiceHealthResponse {
                    service: service_url,
                    status: "healthy".to_string(),
                    message: "Service is responding".to_string(),
                })
            } else {
                Ok(ServiceHealthResponse {
                    service: service_url,
                    status: "unhealthy".to_string(),
                    message: format!("Service returned status: {}", response.status()),
                })
            }
        }
        Err(e) => Ok(ServiceHealthResponse {
            service: service_url,
            status: "error".to_string(),
            message: format!("Failed to connect: {}", e),
        }),
    }
}

// Tauri command to process email files
#[tauri::command]
async fn process_email_file(request: EmailProcessingRequest) -> Result<EmailProcessingResponse, String> {
    let client = reqwest::Client::new();
    let ingestion_url = "http://localhost:8080"; // Default ingestion service URL

    // Send file path to ingestion service
    let response = client
        .post(&format!("{}/process", ingestion_url))
        .json(&request)
        .send()
        .await
        .map_err(|e| format!("Failed to send request: {}", e))?;

    if response.status().is_success() {
        let result: EmailProcessingResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;
        Ok(result)
    } else {
        Err(format!("Processing failed with status: {}", response.status()))
    }
}

#[tauri::command]
async fn get_processed_emails(page: Option<u64>, page_size: Option<u64>) -> Result<EmailListResponse, String> {
    let client = reqwest::Client::new();

    let mut url = "http://localhost:8080/emails".to_string();
    let mut params = Vec::new();

    if let Some(p) = page {
        params.push(format!("page={}", p));
    }
    if let Some(ps) = page_size {
        params.push(format!("page_size={}", ps));
    }

    if !params.is_empty() {
        url.push('?');
        url.push_str(&params.join("&"));
    }

    match client.get(&url).send().await {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<EmailListResponse>().await {
                    Ok(result) => Ok(result),
                    Err(e) => Err(format!("Failed to parse response: {}", e)),
                }
            } else {
                Err(format!("Server error: {}", response.status()))
            }
        }
        Err(e) => Err(format!("Request failed: {}", e)),
    }
}

#[tauri::command]
async fn get_email_details(email_id: String) -> Result<EmailDetailResponse, String> {
    let client = reqwest::Client::new();

    let url = format!("http://localhost:8080/emails/{}", email_id);

    match client.get(&url).send().await {
        Ok(response) => {
            if response.status().is_success() {
                // Parse the response from the ingestion service
                match response.json::<serde_json::Value>().await {
                    Ok(json_response) => {
                        // Extract the email object from the response
                        if let Some(email_obj) = json_response.get("email") {
                            let detail_response = EmailDetailResponse {
                                id: email_obj.get("id")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or(&email_id)
                                    .to_string(),
                                subject: email_obj.get("subject")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string()),
                                from_address: email_obj.get("from_address")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string()),
                                to_addresses: email_obj.get("to_addresses")
                                    .and_then(|v| v.as_array())
                                    .map(|arr| arr.iter()
                                        .filter_map(|v| v.as_str())
                                        .map(|s| s.to_string())
                                        .collect())
                                    .unwrap_or_default(),
                                sent_date: email_obj.get("sent_date")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string()),
                                created_at: email_obj.get("created_at")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                                plain_text_content: email_obj.get("cleaned_plain_text_body")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string()),
                                file_path: email_obj.get("file_path")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string()),
                            };
                            Ok(detail_response)
                        } else {
                            Err("Invalid response format: missing email object".to_string())
                        }
                    }
                    Err(e) => Err(format!("Failed to parse response: {}", e)),
                }
            } else {
                Err(format!("Server error: {}", response.status()))
            }
        }
        Err(e) => Err(format!("Request failed: {}", e)),
    }
}

#[tauri::command]
async fn generate_draft(subject: String, sender: String, content: String) -> Result<DraftResponse, String> {
    let client = reqwest::Client::new();
    let rag_service_url = "http://localhost:8003"; // RAG service URL

    let request = DraftRequest {
        subject,
        sender,
        content,
    };

    // Send request to RAG service
    let response = client
        .post(&format!("{}/generate_draft", rag_service_url))
        .json(&request)
        .send()
        .await
        .map_err(|e| format!("Failed to send request to RAG service: {}", e))?;

    if response.status().is_success() {
        let result: DraftResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse RAG service response: {}", e))?;
        Ok(result)
    } else {
        let status = response.status();
        let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
        Err(format!("RAG service error ({}): {}", status, error_text))
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            check_service_health,
            process_email_file,
            get_processed_emails,
            get_email_details,
            generate_draft
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
