{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 10728265129936428943], [14039947826026167952, "build_script_build", false, 3788274987939205077], [1797035611096599003, "build_script_build", false, 16578987924925656540], [14525517306681678134, "build_script_build", false, 11359670718889654435], [6416823254013318197, "build_script_build", false, 17565975210287166846], [16171925541490437305, "build_script_build", false, 8016521132882550451], [8324462083842905811, "build_script_build", false, 15529040098201205302]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-f28012120f60b92e\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}