{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"h2\", \"http2\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 12434675677363017947, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [784494742817713399, "tower_service", false, 11660862420847627360], [1788832197870803419, "hyper_rustls", false, 462579079470133526], [1906322745568073236, "pin_project_lite", false, 8963179910468966138], [2054153378684941554, "tower_http", false, 14926355108199948429], [2517136641825875337, "sync_wrapper", false, 11531262691852108096], [2883436298747778685, "rustls_pki_types", false, 7785388080773837983], [3150220818285335163, "url", false, 7317158130638989969], [5695049318159433696, "tower", false, 11144354041234974629], [5907992341687085091, "webpki_roots", false, 1751158182921860398], [5986029879202738730, "log", false, 17863289569455453767], [7620660491849607393, "futures_core", false, 5897199496535336083], [8298091525883606470, "cookie_store", false, 8491794688981365988], [9010263965687315507, "http", false, 15629939712500996649], [9689903380558560274, "serde", false, 14326913226427773042], [10229185211513642314, "mime", false, 12149313404929737718], [11895591994124935963, "tokio_rustls", false, 7697247622525185803], [11957360342995674422, "hyper", false, 12330212656273157536], [12393800526703971956, "tokio", false, 9100209665788241157], [13077212702700853852, "base64", false, 17250583037651168056], [14084095096285906100, "http_body", false, 13962944359390943063], [14359893265615549706, "h2", false, 13310183611765353942], [14564311161534545801, "encoding_rs", false, 7150145954793313230], [16066129441945555748, "bytes", false, 2626033548966229242], [16400140949089969347, "rustls", false, 4413186236463235347], [16542808166767769916, "serde_urlencoded", false, 14276380028359400496], [16680807377217054954, "hyper_util", false, 3622868198020901733], [16727543399706004146, "cookie_crate", false, 13023992141537540109], [16900715236047033623, "http_body_util", false, 10967508203520950487]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-1e040fde688adbc2\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}