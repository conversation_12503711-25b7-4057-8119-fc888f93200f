{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 3919467103513695935, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [1200537532907108615, "url<PERSON><PERSON>n", false, 3713524314323759801], [2013030631243296465, "webview2_com", false, 11257737392710050888], [2671782512663819132, "tauri_utils", false, 5071089755032247029], [3150220818285335163, "url", false, 7317158130638989969], [3331586631144870129, "getrandom", false, 5473704362755020652], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [4494683389616423722, "muda", false, 13773507688951389787], [4919829919303820331, "serialize_to_javascript", false, 1571287173107872421], [5986029879202738730, "log", false, 17863289569455453767], [6089812615193535349, "tauri_runtime", false, 13758563210576966044], [7573826311589115053, "tauri_macros", false, 14665103072326406436], [9010263965687315507, "http", false, 15629939712500996649], [9689903380558560274, "serde", false, 14326913226427773042], [10229185211513642314, "mime", false, 12149313404929737718], [10806645703491011684, "thiserror", false, 6923350312884292379], [11599800339996261026, "tauri_runtime_wry", false, 1691974472911755221], [11989259058781683633, "dunce", false, 11426863789755588728], [12393800526703971956, "tokio", false, 9100209665788241157], [12565293087094287914, "window_vibrancy", false, 15150976480604242682], [12986574360607194341, "serde_repr", false, 5766458852573111782], [13077543566650298139, "heck", false, 12227043807642275620], [13625485746686963219, "anyhow", false, 16521415306001099308], [14039947826026167952, "build_script_build", false, 3788274987939205077], [14585479307175734061, "windows", false, 13749503539899060250], [15367738274754116744, "serde_json", false, 6815494230538151593], [16928111194414003569, "dirs", false, 477640866065860759], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-93783647bb9ccd87\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}