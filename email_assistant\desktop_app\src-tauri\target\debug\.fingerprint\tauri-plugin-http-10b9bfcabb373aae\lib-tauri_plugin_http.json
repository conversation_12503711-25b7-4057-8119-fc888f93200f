{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 17331047242322728479, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3713524314323759801], [3150220818285335163, "url", false, 7317158130638989969], [6416823254013318197, "tauri_plugin_fs", false, 14232139825023146997], [7085222851776090619, "reqwest", false, 14759512110485563306], [8298091525883606470, "cookie_store", false, 8491794688981365988], [9010263965687315507, "http", false, 15629939712500996649], [9451456094439810778, "regex", false, 17848222803548847115], [9689903380558560274, "serde", false, 14326913226427773042], [10806645703491011684, "thiserror", false, 6923350312884292379], [12393800526703971956, "tokio", false, 9100209665788241157], [14039947826026167952, "tauri", false, 3165753318189378608], [15367738274754116744, "serde_json", false, 6815494230538151593], [16066129441945555748, "bytes", false, 2626033548966229242], [16171925541490437305, "build_script_build", false, 8016521132882550451], [17047088963840213854, "data_url", false, 6232834256312602976]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-10b9bfcabb373aae\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}