{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 3686219659668893688, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15680059410966760188], [3150220818285335163, "url", false, 11457643946002643913], [6913375703034175521, "schemars", false, 2026813950441894349], [9451456094439810778, "regex", false, 17848222803548847115], [9689903380558560274, "serde", false, 14955956804811053645], [14542658604911247535, "tauri_plugin", false, 11945833665094847650]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-6ff27575bf5c3036\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}