{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 3788274987939205077], [6416823254013318197, "build_script_build", false, 17565975210287166846], [16171925541490437305, "build_script_build", false, 16006910558182991772]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-c0676584debb6d04\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}