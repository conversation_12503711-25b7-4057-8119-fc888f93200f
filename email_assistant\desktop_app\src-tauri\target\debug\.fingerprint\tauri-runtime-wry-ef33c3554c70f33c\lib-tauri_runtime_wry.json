{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6620145502275485423, "deps": [[376837177317575824, "softbuffer", false, 17414782513918790710], [2013030631243296465, "webview2_com", false, 11257737392710050888], [2671782512663819132, "tauri_utils", false, 5071089755032247029], [3150220818285335163, "url", false, 7317158130638989969], [3722963349756955755, "once_cell", false, 14521976164538839542], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [5986029879202738730, "log", false, 17863289569455453767], [6089812615193535349, "tauri_runtime", false, 13758563210576966044], [8826339825490770380, "tao", false, 11582385607375637568], [9010263965687315507, "http", false, 15629939712500996649], [9141053277961803901, "wry", false, 12114954182727249095], [11599800339996261026, "build_script_build", false, 16011662610975593677], [14585479307175734061, "windows", false, 13749503539899060250]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ef33c3554c70f33c\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}