{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 4128467976108975410, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15680059410966760188], [3060637413840920116, "proc_macro2", false, 5373005460213438867], [3150220818285335163, "url", false, 11457643946002643913], [3191507132440681679, "serde_untagged", false, 2488351980191379651], [4071963112282141418, "serde_with", false, 1799651832287629157], [4899080583175475170, "semver", false, 16636169827845809847], [5986029879202738730, "log", false, 16544253528419911208], [6606131838865521726, "ctor", false, 8001462743388477718], [6913375703034175521, "schemars", false, 2026813950441894349], [7170110829644101142, "json_patch", false, 5451797175141292056], [8319709847752024821, "uuid", false, 17523704281557203773], [9010263965687315507, "http", false, 15629939712500996649], [9451456094439810778, "regex", false, 17848222803548847115], [9556762810601084293, "brotli", false, 4284906626722745546], [9689903380558560274, "serde", false, 14955956804811053645], [10806645703491011684, "thiserror", false, 6923350312884292379], [11655476559277113544, "cargo_metadata", false, 5290758483480028769], [11989259058781683633, "dunce", false, 11426863789755588728], [13625485746686963219, "anyhow", false, 16521415306001099308], [14232843520438415263, "html5ever", false, 7319515928715295293], [15088007382495681292, "kuchiki", false, 7052890181816346442], [15367738274754116744, "serde_json", false, 9951868567464697481], [15609422047640926750, "toml", false, 4825197427743863217], [15622660310229662834, "walkdir", false, 4450029676203649764], [15932120279885307830, "memchr", false, 10200081116297473958], [17146114186171651583, "infer", false, 17453765660541990643], [17155886227862585100, "glob", false, 16666287207728800018], [17186037756130803222, "phf", false, 7206095472792032900], [17990358020177143287, "quote", false, 9227116837396871820]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-827843f0b8603b23\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}