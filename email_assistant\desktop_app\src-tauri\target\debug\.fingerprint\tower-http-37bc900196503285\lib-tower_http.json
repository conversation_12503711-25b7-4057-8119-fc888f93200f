{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 9958066978812107341, "deps": [[784494742817713399, "tower_service", false, 11660862420847627360], [1906322745568073236, "pin_project_lite", false, 8963179910468966138], [4121350475192885151, "iri_string", false, 14374887090994774795], [5695049318159433696, "tower", false, 11144354041234974629], [7712452662827335977, "tower_layer", false, 8862439362659442001], [7896293946984509699, "bitflags", false, 5188561256486809628], [9010263965687315507, "http", false, 15629939712500996649], [10629569228670356391, "futures_util", false, 9491360215915417701], [14084095096285906100, "http_body", false, 13962944359390943063], [16066129441945555748, "bytes", false, 2626033548966229242]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-37bc900196503285\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}