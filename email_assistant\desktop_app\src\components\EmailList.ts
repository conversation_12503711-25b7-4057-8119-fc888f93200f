// AI-Assisted Email Response System - Email List Component

import { invoke } from '@tauri-apps/api/core';
import type { EmailSummary, EmailListResponse, EmailDetailResponse } from '../types';

export class EmailList {
  private container: HTMLElement;
  private emails: EmailSummary[] = [];
  private loading: boolean = false;
  private currentPage: number = 1;
  private pageSize: number = 20;
  private totalCount: number = 0;
  private selectedEmailId: string | null = null;
  private onEmailSelect: ((email: EmailSummary) => void) | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.render();
    this.loadEmails();
  }

  public setOnEmailSelect(callback: (email: EmailSummary) => void) {
    this.onEmailSelect = callback;
  }

  private async loadEmails() {
    this.setLoading(true);
    try {
      const response: EmailListResponse = await invoke('get_processed_emails', {
        page: this.currentPage,
        pageSize: this.pageSize
      });
      
      this.emails = response.emails;
      this.totalCount = response.total_count;
      this.currentPage = response.page;
      this.pageSize = response.page_size;
      
      this.renderEmailList();
    } catch (error) {
      this.renderError(`Failed to load emails: ${error}`);
    } finally {
      this.setLoading(false);
    }
  }

  private setLoading(loading: boolean) {
    this.loading = loading;
    this.renderLoadingState();
  }

  private render() {
    this.container.innerHTML = `
      <div class="email-list-container">
        <div class="email-list-header">
          <h2>Processed Emails</h2>
          <div class="email-list-controls">
            <span class="sort-indicator">📅 Newest First</span>
            <button id="refresh-emails" class="btn btn-secondary">
              <span class="icon">🔄</span>
              Refresh
            </button>
          </div>
        </div>
        <div class="email-list-content">
          <div id="email-list-loading" class="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Loading emails...</p>
          </div>
          <div id="email-list-error" class="error-state" style="display: none;"></div>
          <div id="email-list-items" class="email-list-items"></div>
          <div id="email-list-pagination" class="pagination-container"></div>
        </div>
      </div>
    `;

    // Add event listeners
    const refreshBtn = this.container.querySelector('#refresh-emails') as HTMLButtonElement;
    refreshBtn?.addEventListener('click', () => this.loadEmails());
  }

  private renderLoadingState() {
    const loadingEl = this.container.querySelector('#email-list-loading') as HTMLElement;
    const contentEl = this.container.querySelector('#email-list-items') as HTMLElement;
    
    if (this.loading) {
      loadingEl.style.display = 'flex';
      contentEl.style.display = 'none';
    } else {
      loadingEl.style.display = 'none';
      contentEl.style.display = 'block';
    }
  }

  private renderError(message: string) {
    const errorEl = this.container.querySelector('#email-list-error') as HTMLElement;
    const contentEl = this.container.querySelector('#email-list-items') as HTMLElement;
    
    errorEl.innerHTML = `
      <div class="error-message">
        <span class="icon">⚠️</span>
        <p>${message}</p>
        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
      </div>
    `;
    
    errorEl.style.display = 'block';
    contentEl.style.display = 'none';
  }

  private renderEmailList() {
    const listEl = this.container.querySelector('#email-list-items') as HTMLElement;
    const errorEl = this.container.querySelector('#email-list-error') as HTMLElement;

    errorEl.style.display = 'none';

    if (this.emails.length === 0) {
      listEl.innerHTML = `
        <div class="empty-state">
          <span class="icon">📧</span>
          <h3>No emails found</h3>
          <p>Process some email files to see them here.</p>
        </div>
      `;
      return;
    }

    // Sort emails by date (newest to oldest)
    const sortedEmails = [...this.emails].sort((a, b) => {
      const dateA = a.sent_date ? new Date(a.sent_date).getTime() : 0;
      const dateB = b.sent_date ? new Date(b.sent_date).getTime() : 0;
      return dateB - dateA; // Newest first
    });

    // Show all emails (they will be scrollable)
    const emailItems = sortedEmails.map(email => this.renderEmailItem(email)).join('');
    listEl.innerHTML = emailItems;

    // Add click and double-click event listeners to email items
    listEl.querySelectorAll('.email-item').forEach((item, index) => {
      item.addEventListener('click', () => {
        this.selectEmail(sortedEmails[index]);
      });

      item.addEventListener('dblclick', () => {
        this.showEmailModal(sortedEmails[index]);
      });
    });

    this.renderPagination();
  }

  private renderEmailItem(email: EmailSummary): string {
    const isSelected = this.selectedEmailId === email.id;
    const sentDate = email.sent_date ? new Date(email.sent_date).toLocaleDateString() : 'Unknown';
    const fromAddress = email.from_address || 'Unknown sender';
    const subject = email.subject || '(No subject)';
    const preview = email.preview || 'No preview available';

    return `
      <div class="email-item ${isSelected ? 'selected' : ''}" data-email-id="${email.id}">
        <div class="email-item-header">
          <div class="email-subject">${this.escapeHtml(subject)}</div>
          <div class="email-date">${sentDate}</div>
        </div>
        <div class="email-from">${this.escapeHtml(fromAddress)}</div>
        <div class="email-preview">${this.escapeHtml(preview)}</div>
        <div class="email-recipients">
          To: ${email.to_addresses.map(addr => this.escapeHtml(addr)).join(', ')}
        </div>
      </div>
    `;
  }

  private renderPagination() {
    const paginationEl = this.container.querySelector('#email-list-pagination') as HTMLElement;
    
    if (this.totalCount <= this.pageSize) {
      paginationEl.style.display = 'none';
      return;
    }

    const totalPages = Math.ceil(this.totalCount / this.pageSize);
    const startItem = (this.currentPage - 1) * this.pageSize + 1;
    const endItem = Math.min(this.currentPage * this.pageSize, this.totalCount);

    paginationEl.innerHTML = `
      <div class="pagination">
        <button class="btn btn-secondary" ${this.currentPage <= 1 ? 'disabled' : ''}
                onclick="this.previousPage()">Previous</button>
        <span class="pagination-info">
          Showing all ${this.emails.length} emails (${startItem}-${endItem} of ${this.totalCount} total)
        </span>
        <button class="btn btn-secondary" ${this.currentPage >= totalPages ? 'disabled' : ''}
                onclick="this.nextPage()">Next</button>
      </div>
    `;

    paginationEl.style.display = 'flex';
  }

  private selectEmail(email: EmailSummary) {
    this.selectedEmailId = email.id;
    this.renderEmailList(); // Re-render to update selection
    
    if (this.onEmailSelect) {
      this.onEmailSelect(email);
    }
  }

  public previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadEmails();
    }
  }

  public nextPage() {
    const totalPages = Math.ceil(this.totalCount / this.pageSize);
    if (this.currentPage < totalPages) {
      this.currentPage++;
      this.loadEmails();
    }
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  public refresh() {
    this.loadEmails();
  }

  public getSelectedEmail(): EmailSummary | null {
    return this.emails.find(email => email.id === this.selectedEmailId) || null;
  }

  private async showEmailModal(email: EmailSummary) {
    try {
      // Fetch full email details
      const emailDetail: EmailDetailResponse = await invoke('get_email_details', {
        emailId: email.id
      });

      // Create modal
      this.createEmailModal(emailDetail);
    } catch (error) {
      console.error('Failed to load email details for modal:', error);
      // Show error in a simple alert for now
      alert(`Failed to load email details: ${error}`);
    }
  }

  private createEmailModal(email: EmailDetailResponse) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.email-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal element
    const modal = document.createElement('div');
    modal.className = 'email-modal';

    const sentDate = email.sent_date
      ? new Date(email.sent_date).toLocaleString()
      : 'Unknown';
    const subject = email.subject || '(No subject)';
    const fromAddress = email.from_address || 'Unknown sender';
    const content = email.plain_text_content || 'No content available';
    const toAddresses = email.to_addresses.join(', ') || 'Unknown recipients';

    modal.innerHTML = `
      <div class="email-modal-content">
        <div class="email-modal-header">
          <h2 class="email-modal-title">${this.escapeHtml(subject)}</h2>
          <button class="email-modal-close" aria-label="Close modal">&times;</button>
        </div>
        <div class="email-modal-body">
          <div class="email-modal-metadata">
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">From:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(fromAddress)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">To:</span>
              <span class="email-modal-metadata-value">${this.escapeHtml(toAddresses)}</span>
            </div>
            <div class="email-modal-metadata-row">
              <span class="email-modal-metadata-label">Date:</span>
              <span class="email-modal-metadata-value">${sentDate}</span>
            </div>
          </div>
          <div class="email-modal-content-section">
            <pre class="email-modal-text">${this.escapeHtml(content)}</pre>
          </div>
        </div>
      </div>
    `;

    // Add modal to document
    document.body.appendChild(modal);

    // Add event listeners
    const closeBtn = modal.querySelector('.email-modal-close');
    closeBtn?.addEventListener('click', () => this.closeEmailModal());

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeEmailModal();
      }
    });

    // Close on ESC key
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        this.closeEmailModal();
        document.removeEventListener('keydown', handleEscKey);
      }
    };
    document.addEventListener('keydown', handleEscKey);

    // Show modal with animation
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);
  }

  private closeEmailModal() {
    const modal = document.querySelector('.email-modal');
    if (modal) {
      modal.classList.remove('show');
      setTimeout(() => {
        modal.remove();
      }, 150); // Match transition duration
    }
  }
}
