// AI-Assisted Email Response System - Ingestion Service Library
// Core functionality for email parsing, cleaning, and database operations

use std::fs;
use std::io;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use dotenv::dotenv;
use reqwest::Client;
use qdrant_client::{
    Qdrant,
    qdrant::{
        CreateCollection, Distance, VectorParams,
        PointStruct, SearchPoints, UpsertPoints, ScrollPoints, Value, WithPayloadSelector, WithVectorsSelector
    },
};
use std::collections::HashMap;
use mail_parser::MessageParser;

// Email parsing and cleaning functions

/// Converts HTML content to plain text with proper formatting.
/// Uses html2text crate for robust HTML parsing and conversion.
pub fn html_to_plain_text(html: &str) -> String {
    html2text::from_read(html.as_bytes(), 80)
}

/// Strips email signatures and quoted content from email text.
/// Removes lines starting with '>' (quoted content) and content after signature markers.
pub fn strip_signatures_and_quotes(text: &str) -> String {
    let mut cleaned_lines = Vec::new();
    
    for line in text.lines() {
        // Check for common signature markers
        if line.trim() == "--" || 
           (line.trim().starts_with("-- ") && line.trim().len() < 50) ||
           line.trim().starts_with("---") ||
           line.contains("Sent from my") ||
           line.contains("Best regards") ||
           line.contains("Kind regards") {
            break; // Stop processing once we hit a signature
        }
        
        // Skip quoted content (lines starting with >)
        if line.starts_with('>') || line.starts_with(" >") {
            continue;
        }
        
        // Skip forwarded message headers
        if line.starts_with("-----Original Message-----") ||
           line.starts_with("From:") && cleaned_lines.is_empty() {
            continue;
        }
        
        cleaned_lines.push(line);
    }
    
    // Remove excessive whitespace at the end
    while let Some(last_line) = cleaned_lines.last() {
        if last_line.trim().is_empty() {
            cleaned_lines.pop();
        } else {
            break;
        }
    }
    
    cleaned_lines.join("\n")
}

// Email struct for parsed emails
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ParsedEmail {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
}

// Message struct for Qdrant storage (metadata stored as payload)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
    pub file_path: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Qdrant search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub message: Message,
    pub score: f32,
}

impl From<ParsedEmail> for Message {
    fn from(parsed: ParsedEmail) -> Self {
        let now = Utc::now();
        Message {
            id: parsed.id,
            subject: parsed.subject,
            from_address: parsed.from,
            to_addresses: parsed.to,
            sent_date: parsed.sent_date,
            plain_text_body_raw: parsed.plain_text_body_raw,
            html_body_raw: parsed.html_body_raw,
            cleaned_plain_text_body: parsed.cleaned_plain_text_body,
            file_path: None,
            created_at: now,
            updated_at: now,
        }
    }
}

/// Parses a single .eml file into a ParsedEmail struct.
/// Applies cleaning functions and handles errors.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read_to_string(file_path)?;
    parse_email_from_string(&content)
}

/// Parses an mbox file and returns a vector of ParsedEmail structs.
/// Uses binary-safe reading to handle different encodings and attachments.
pub fn parse_mbox(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    // Read file as bytes to preserve encoding
    let bytes = fs::read(file_path)?;
    let mut emails = Vec::new();
    let mut current_email_bytes = Vec::new();
    let mut in_email = false;
    let mut line_start = 0;

    // Process the mbox file line by line in bytes to preserve encoding
    for i in 0..bytes.len() {
        if bytes[i] == b'\n' || i == bytes.len() - 1 {
            let line_end = if i == bytes.len() - 1 { i + 1 } else { i };
            let line_bytes = &bytes[line_start..line_end];

            // Convert line to string for pattern matching (this is safe for ASCII patterns)
            let line_str = String::from_utf8_lossy(line_bytes);
            let line_str = line_str.trim_end_matches('\r'); // Handle CRLF

            // Check for mbox separator lines
            if (line_str.starts_with("From ") && line_str.contains("@")) ||
               (line_str.starts_with("From - ")) {
                // Process the previous email if we have one
                if in_email && !current_email_bytes.is_empty() {
                    match parse_email_from_bytes(&current_email_bytes) {
                        Ok(parsed_email) => emails.push(parsed_email),
                        Err(e) => {
                            eprintln!("Warning: Failed to parse email in mbox: {}", e);
                            // Continue processing other emails
                        }
                    }
                }
                // Start a new email (skip the "From " separator line)
                current_email_bytes.clear();
                in_email = true;
            } else if in_email {
                // Add the line bytes to current email, preserving original encoding
                current_email_bytes.extend_from_slice(line_bytes);
                if i < bytes.len() - 1 {
                    current_email_bytes.push(b'\n');
                }
            }

            line_start = i + 1;
        }
    }

    // Process the last email
    if in_email && !current_email_bytes.is_empty() {
        match parse_email_from_bytes(&current_email_bytes) {
            Ok(parsed_email) => emails.push(parsed_email),
            Err(e) => {
                eprintln!("Warning: Failed to parse last email in mbox: {}", e);
            }
        }
    }

    println!("Successfully parsed {} emails from mbox file", emails.len());
    Ok(emails)
}

/// Helper function to parse email from bytes using mail-parser crate
/// Extracts only text content, filtering out binary attachments and non-text parts
/// This preserves the original encoding and lets mail-parser handle character set detection
fn parse_email_from_bytes(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    // Parse the email using mail-parser with full encoding support
    let message = MessageParser::default()
        .parse(content)
        .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

    parse_message_to_email(message)
}

/// Helper function to parse email from string content using mail-parser crate
/// Extracts only text content, filtering out binary attachments and non-text parts
fn parse_email_from_string(content: &str) -> Result<ParsedEmail, io::Error> {
    // Parse the email using mail-parser
    let message = MessageParser::default()
        .parse(content.as_bytes())
        .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

    parse_message_to_email(message)
}

/// Common function to extract data from a parsed mail-parser Message
fn parse_message_to_email(message: mail_parser::Message) -> Result<ParsedEmail, io::Error> {

    // Extract basic header information
    let subject = message.subject().map(|s| s.to_string());
    let from = message.from()
        .and_then(|addr_list| addr_list.first())
        .and_then(|addr| addr.address())
        .map(|s| s.to_string());

    let mut to = Vec::new();
    if let Some(to_addrs) = message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address() {
                to.push(address.to_string());
            }
        }
    }

    let sent_date = message.date().map(|dt| {
        // Convert mail-parser DateTime to chrono DateTime<Utc>
        DateTime::<Utc>::from_timestamp(dt.to_timestamp(), 0).unwrap_or_else(|| Utc::now())
    });

    // Extract text content only - this automatically excludes binary attachments
    let mut plain_text_parts = Vec::new();
    let mut html_parts = Vec::new();

    // Collect all text body parts (excludes attachments and binary content)
    for i in 0..message.text_body_count() {
        if let Some(text) = message.body_text(i) {
            plain_text_parts.push(text.to_string());
        }
    }

    // Collect all HTML body parts and convert to plain text
    for i in 0..message.html_body_count() {
        if let Some(html) = message.body_html(i) {
            html_parts.push(html.to_string());
        }
    }

    // Combine plain text parts
    let plain_text_body_raw = if !plain_text_parts.is_empty() {
        Some(plain_text_parts.join("\n\n"))
    } else {
        None
    };

    // Combine HTML parts
    let html_body_raw = if !html_parts.is_empty() {
        Some(html_parts.join("\n\n"))
    } else {
        None
    };

    // Create cleaned plain text body
    let cleaned_plain_text_body = if let Some(html) = &html_body_raw {
        // Convert HTML to plain text and clean it
        let plain_from_html = html_to_plain_text(html);
        Some(strip_signatures_and_quotes(&plain_from_html))
    } else if let Some(plain) = &plain_text_body_raw {
        // Clean the plain text
        Some(strip_signatures_and_quotes(plain))
    } else {
        None
    };

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date,
        plain_text_body_raw,
        html_body_raw,
        cleaned_plain_text_body,
    })
}

/// Establishes a connection to Qdrant vector database.
pub async fn establish_connection() -> Result<Qdrant, Box<dyn std::error::Error + Send + Sync>> {
    dotenv().ok(); // Load .env file if present

    let qdrant_url = std::env::var("QDRANT_URL")
        .unwrap_or_else(|_| "http://localhost:6334".to_string());

    let client = Qdrant::from_url(&qdrant_url).build()?;
    Ok(client)
}

/// Sets up Qdrant collections for email storage.
pub async fn setup_collections(client: &Qdrant) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Check if collection exists, create if not
    match client.collection_info(collection_name).await {
        Ok(_) => {
            println!("✓ Collection '{}' already exists", collection_name);
        }
        Err(_) => {
            println!("Creating collection '{}'...", collection_name);

            let create_collection = CreateCollection {
                collection_name: collection_name.to_string(),
                vectors_config: Some(VectorParams {
                    size: 384, // sentence-transformers/all-MiniLM-L6-v2 dimension
                    distance: Distance::Cosine as i32,
                    ..Default::default()
                }.into()),
                ..Default::default()
            };

            client.create_collection(create_collection).await?;
            println!("✓ Collection '{}' created successfully", collection_name);
        }
    }

    Ok(())
}

/// Inserts a message with embedding into Qdrant.
pub async fn insert_message_with_embedding(
    client: &Qdrant,
    message: &Message,
    embedding: Vec<f32>
) -> Result<Uuid, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Create payload with message metadata
    let mut payload = HashMap::new();
    payload.insert("id".to_string(), Value::from(message.id.to_string()));
    payload.insert("subject".to_string(), Value::from(message.subject.clone().unwrap_or_default()));
    payload.insert("from_address".to_string(), Value::from(message.from_address.clone().unwrap_or_default()));
    payload.insert("to_addresses".to_string(), Value::from(serde_json::to_string(&message.to_addresses)?));
    payload.insert("sent_date".to_string(), Value::from(message.sent_date.map(|d| d.to_rfc3339()).unwrap_or_default()));
    payload.insert("plain_text_body_raw".to_string(), Value::from(message.plain_text_body_raw.clone().unwrap_or_default()));
    payload.insert("html_body_raw".to_string(), Value::from(message.html_body_raw.clone().unwrap_or_default()));
    payload.insert("cleaned_plain_text_body".to_string(), Value::from(message.cleaned_plain_text_body.clone().unwrap_or_default()));
    payload.insert("file_path".to_string(), Value::from(message.file_path.clone().unwrap_or_default()));
    payload.insert("created_at".to_string(), Value::from(message.created_at.to_rfc3339()));
    payload.insert("updated_at".to_string(), Value::from(message.updated_at.to_rfc3339()));

    // Create point with vector and payload
    let point = PointStruct::new(
        message.id.to_string(),
        embedding,
        payload,
    );

    // Upsert point to collection
    let upsert_request = UpsertPoints {
        collection_name: collection_name.to_string(),
        points: vec![point],
        ..Default::default()
    };
    client.upsert_points(upsert_request).await?;

    Ok(message.id)
}

/// Searches for similar messages using vector similarity.
pub async fn search_similar_messages(
    client: &Qdrant,
    query_embedding: Vec<f32>,
    limit: Option<u64>,
    score_threshold: Option<f32>
) -> Result<Vec<SearchResult>, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";
    let limit = limit.unwrap_or(10);

    let search_result = client.search_points(SearchPoints {
        collection_name: collection_name.to_string(),
        vector: query_embedding,
        limit,
        score_threshold,
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        with_vectors: Some(WithVectorsSelector {
            selector_options: Some(qdrant_client::qdrant::with_vectors_selector::SelectorOptions::Enable(false)),
        }),
        ..Default::default()
    }).await?;

    let mut results = Vec::new();
    for scored_point in search_result.result {
        let payload = scored_point.payload;
            let id_str = payload.get("id").unwrap().to_string();
            let subject_str = payload.get("subject").unwrap().to_string();
            let from_str = payload.get("from_address").unwrap().to_string();
            let to_addresses_str = payload.get("to_addresses").unwrap().to_string();
            let sent_date_str = payload.get("sent_date").unwrap().to_string();
            let plain_text_str = payload.get("plain_text_body_raw").unwrap().to_string();
            let html_str = payload.get("html_body_raw").unwrap().to_string();
            let cleaned_str = payload.get("cleaned_plain_text_body").unwrap().to_string();
            let file_path_str = payload.get("file_path").unwrap().to_string();
            let created_at_str = payload.get("created_at").unwrap().to_string();
            let updated_at_str = payload.get("updated_at").unwrap().to_string();

            let message = Message {
                id: Uuid::parse_str(&id_str.trim_matches('"'))?,
                subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                to_addresses: safe_parse_json_array(&to_addresses_str)?,
                sent_date: Some(sent_date_str.trim_matches('"'))
                    .filter(|s| !s.is_empty())
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),
            };

        results.push(SearchResult {
            message,
            score: scored_point.score,
        });
    }

    Ok(results)
}

/// Gets recent messages (without vector search).
/// When no limit is specified, returns last 200 emails (prioritizing received emails).
pub async fn get_recent_messages(
    client: &Qdrant,
    limit: Option<u64>
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Fetch more messages than needed to allow filtering and sorting
    let mut all_messages = Vec::new();
    let mut offset = None;
    let target_limit = limit.unwrap_or(200); // Default to 200 emails
    let fetch_limit = if limit.is_none() { 1000 } else { target_limit }; // Fetch more to filter

    loop {
        let scroll_request = ScrollPoints {
            collection_name: collection_name.to_string(),
            limit: Some(std::cmp::min(fetch_limit, 1000) as u32), // Batch size
            offset,
            with_payload: Some(WithPayloadSelector {
                selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
            }),
            with_vectors: Some(WithVectorsSelector {
                selector_options: Some(qdrant_client::qdrant::with_vectors_selector::SelectorOptions::Enable(false)),
            }),
            ..Default::default()
        };
        let scroll_result = client.scroll(scroll_request).await?;

        for point in scroll_result.result {
            let payload = point.payload;
            let id_str = payload.get("id").unwrap().to_string();
            let subject_str = payload.get("subject").unwrap().to_string();
            let from_str = payload.get("from_address").unwrap().to_string();
            let to_addresses_str = payload.get("to_addresses").unwrap().to_string();
            let sent_date_str = payload.get("sent_date").unwrap().to_string();
            let plain_text_str = payload.get("plain_text_body_raw").unwrap().to_string();
            let html_str = payload.get("html_body_raw").unwrap().to_string();
            let cleaned_str = payload.get("cleaned_plain_text_body").unwrap().to_string();
            let file_path_str = payload.get("file_path").unwrap().to_string();
            let created_at_str = payload.get("created_at").unwrap().to_string();
            let updated_at_str = payload.get("updated_at").unwrap().to_string();

            let message = Message {
                id: Uuid::parse_str(&id_str.trim_matches('"'))?,
                subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                to_addresses: safe_parse_json_array(&to_addresses_str)?,
                sent_date: Some(sent_date_str.trim_matches('"'))
                    .filter(|s| !s.is_empty())
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),
            };

            // Collect all messages for now
            all_messages.push(message);
        }

        // Check if we should continue fetching
        if all_messages.len() >= fetch_limit as usize {
            break; // We have enough messages for processing
        }

        // Check if there are more results
        if let Some(next_offset) = scroll_result.next_page_offset {
            offset = Some(next_offset);
        } else {
            break; // No more results
        }
    }

    // Post-process the messages when no specific limit is provided
    if limit.is_none() {
        // Sort by date (newest first) and filter for received emails
        all_messages.sort_by(|a, b| {
            let date_a = a.sent_date.unwrap_or(a.created_at);
            let date_b = b.sent_date.unwrap_or(b.created_at);
            date_b.cmp(&date_a) // Newest first
        });

        // Filter for received emails (simple heuristic)
        let received_emails: Vec<Message> = all_messages
            .into_iter()
            .filter(|msg| {
                // Consider it a received email if:
                // 1. Has a from_address (external sender)
                // 2. Has to_addresses (recipients)
                // 3. Doesn't look like automated/system emails
                if let Some(from_addr) = &msg.from_address {
                    !from_addr.is_empty() &&
                    !msg.to_addresses.is_empty() &&
                    !from_addr.contains("noreply") &&
                    !from_addr.contains("no-reply") &&
                    !from_addr.starts_with("mailer-daemon") &&
                    !from_addr.contains("postmaster")
                } else {
                    false
                }
            })
            .take(target_limit as usize)
            .collect();

        Ok(received_emails)
    } else {
        // For specific limits, return as-is (original behavior)
        all_messages.truncate(target_limit as usize);
        Ok(all_messages)
    }
}

/// Processes a single email file, generates embedding, and stores in Qdrant.
pub async fn ingest_email_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Uuid, Box<dyn std::error::Error + Send + Sync>> {
    let parsed_email = if file_path.ends_with(".mbox") {
        // For mbox files, take the first email for now
        let emails = parse_mbox(file_path)?;
        if emails.is_empty() {
            return Err("No emails found in mbox file".into());
        }
        emails.into_iter().next().unwrap()
    } else {
        parse_eml(file_path)?
    };

    let mut message = Message::from(parsed_email);
    message.file_path = Some(file_path.to_string());

    // Generate embedding for the cleaned text
    if let Some(cleaned_text) = &message.cleaned_plain_text_body {
        if !cleaned_text.trim().is_empty() {
            let embedding = get_embedding(cleaned_text, embedding_service_url).await?;
            let message_id = insert_message_with_embedding(client, &message, embedding).await?;
            println!("✓ Ingested email with embedding: {} (ID: {})", file_path, message_id);
            Ok(message_id)
        } else {
            Err("No cleaned text available for embedding".into())
        }
    } else {
        Err("No cleaned text available for embedding".into())
    }
}

/// Processes all emails from an mbox file with embeddings.
pub async fn ingest_mbox_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Vec<Uuid>, Box<dyn std::error::Error + Send + Sync>> {
    let emails = parse_mbox(file_path)?;
    let mut ingested_ids = Vec::new();

    for parsed_email in emails {
        let mut message = Message::from(parsed_email);
        message.file_path = Some(file_path.to_string());

        if let Some(cleaned_text) = &message.cleaned_plain_text_body {
            if !cleaned_text.trim().is_empty() {
                match get_embedding(cleaned_text, embedding_service_url).await {
                    Ok(embedding) => {
                        match insert_message_with_embedding(client, &message, embedding).await {
                            Ok(id) => {
                                ingested_ids.push(id);
                                println!("✓ Ingested email from mbox: {}", id);
                            }
                            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
                        }
                    }
                    Err(e) => eprintln!("Warning: Failed to generate embedding: {}", e),
                }
            } else {
                println!("⚠ Skipping email with empty text");
            }
        } else {
            println!("⚠ Skipping email with no cleaned text");
        }
    }

    println!("✓ Ingested {} emails from mbox: {}", ingested_ids.len(), file_path);
    Ok(ingested_ids)
}

// Embedding service integration

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    text: String,
    normalize: Option<bool>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingResponse {
    embedding: Vec<f32>,
    #[allow(dead_code)]
    model_name: String,
    #[allow(dead_code)]
    dimension: usize,
}

/// Detects if a file is a Thunderbird mbox file (no extension, contains email headers)
pub fn is_thunderbird_mbox(file_path: &str) -> bool {
    use std::path::Path;

    let path = Path::new(file_path);

    // Check if file has no extension
    if path.extension().is_some() {
        return false;
    }

    // Check if it's in a .sbd directory or has typical Thunderbird names
    let parent_dir = path.parent().map(|p| p.to_string_lossy().to_lowercase());
    let file_name = path.file_name().map(|n| n.to_string_lossy().to_lowercase());

    if let Some(parent) = parent_dir {
        if parent.contains(".sbd") {
            return true;
        }
    }

    // Check for common Thunderbird folder names
    if let Some(name) = file_name {
        if ["inbox", "sent", "drafts", "trash", "junk", "outbox", "templates"].contains(&name.as_str()) {
            return true;
        }
    }

    // Try to read first few lines to detect mbox format (handle binary data)
    if let Ok(bytes) = std::fs::read(file_path) {
        // Try to convert first 2KB to string, ignoring invalid UTF-8
        let preview_size = std::cmp::min(2048, bytes.len());
        let preview = String::from_utf8_lossy(&bytes[..preview_size]);
        let lines: Vec<&str> = preview.lines().take(10).collect();
        for line in lines {
            // Look for typical mbox "From " separator or Thunderbird "From - " separator
            if (line.starts_with("From ") && line.contains("@")) ||
               line.starts_with("From - ") {
                return true;
            }
            // Look for Mozilla/Thunderbird specific headers
            if line.starts_with("X-Mozilla-Status:") ||
               line.starts_with("X-Mozilla-Status2:") {
                return true;
            }
            // Look for standard email headers
            if line.starts_with("Return-Path:") ||
               line.starts_with("Delivered-To:") ||
               line.starts_with("Received:") ||
               line.starts_with("Message-ID:") {
                return true;
            }
        }
    }

    false
}

/// Helper function to safely parse JSON from Qdrant payload
fn safe_parse_json_array(json_str: &str) -> Result<Vec<String>, Box<dyn std::error::Error + Send + Sync>> {
    let trimmed = json_str.trim_matches('"');

    // Try parsing directly first
    if let Ok(result) = serde_json::from_str::<Vec<String>>(trimmed) {
        return Ok(result);
    }

    // If that fails, try parsing as a string and then parsing the inner content
    if let Ok(inner_str) = serde_json::from_str::<String>(trimmed) {
        if let Ok(result) = serde_json::from_str::<Vec<String>>(&inner_str) {
            return Ok(result);
        }
    }

    // Fallback: return empty array
    Ok(vec![])
}

/// Generates an embedding for the given text using the embedding service.
pub async fn get_embedding(text: &str, embedding_service_url: &str) -> Result<Vec<f32>, Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();

    let request = EmbeddingRequest {
        text: text.to_string(),
        normalize: Some(true),
    };

    let response = client
        .post(&format!("{}/embed", embedding_service_url))
        .json(&request)
        .send()
        .await?;

    if response.status().is_success() {
        let embedding_response: EmbeddingResponse = response.json().await?;
        Ok(embedding_response.embedding)
    } else {
        let error_text = response.text().await?;
        Err(format!("Embedding service error: {}", error_text).into())
    }
}



#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_html_to_plain_text() {
        let html_content = "<html><body><h1>Hello</h1><p>This is <b>HTML</b> with a <a href=\"http://example.com\">link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>";
        let plain_text = html_to_plain_text(html_content);
        assert!(plain_text.contains("Hello"));
        assert!(plain_text.contains("HTML"));
        assert!(plain_text.contains("Item 1"));
        assert!(plain_text.contains("Item 2"));
    }

    #[test]
    fn test_strip_signatures_and_quotes() {
        let text_with_signature = "Hello,\n\nThis is the main body.\n\n-- \nJohn Doe\nSoftware Engineer";
        let cleaned = strip_signatures_and_quotes(text_with_signature);
        assert_eq!(cleaned, "Hello,\n\nThis is the main body.");

        let text_with_quote = "Hello,\n\nMy reply.\n\n> On Mon, Jan 1, 2023 at 10:00 AM John Doe <<EMAIL>> wrote:\n> This is a quoted message.";
        let cleaned_quote = strip_signatures_and_quotes(text_with_quote);
        assert_eq!(cleaned_quote, "Hello,\n\nMy reply.");
    }

    #[test]
    fn test_parse_valid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Test Subject".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_email.to, vec!["<EMAIL>".to_string()]);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_single_email() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert_eq!(email.subject, Some("Test Subject".to_string()));
        assert_eq!(email.from, Some("<EMAIL>".to_string()));
        assert_eq!(email.to, vec!["<EMAIL>".to_string()]);
        assert!(email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_multiple_emails() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: First Email\nTo: <EMAIL>\n\nFirst email content.\n\nFrom <EMAIL> Mon Jan  1 11:00:00 2024\nFrom: <EMAIL>\nSubject: Second Email\nTo: <EMAIL>\n\nSecond email content.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 2);

        assert_eq!(parsed_emails[0].subject, Some("First Email".to_string()));
        assert_eq!(parsed_emails[0].from, Some("<EMAIL>".to_string()));

        assert_eq!(parsed_emails[1].subject, Some("Second Email".to_string()));
        assert_eq!(parsed_emails[1].from, Some("<EMAIL>".to_string()));
        Ok(())
    }

    #[test]
    fn test_parse_eml_with_attachments() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Email with Attachment\nTo: <EMAIL>\nContent-Type: multipart/mixed; boundary=\"boundary123\"\n\n--boundary123\nContent-Type: text/plain\n\nThis email has an attachment.\n\n--boundary123\nContent-Type: application/pdf\nContent-Disposition: attachment; filename=\"document.pdf\"\n\nPDF content here\n--boundary123--";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Email with Attachment".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_eml_html_content() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: HTML Email\nTo: <EMAIL>\nContent-Type: text/html\n\n<html><body><h1>Hello</h1><p>This is an HTML email.</p></body></html>";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("HTML Email".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Should extract plain text from HTML
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("Hello"));
            assert!(body.contains("This is an HTML email"));
            assert!(!body.contains("<html>"));
            assert!(!body.contains("<body>"));
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_missing_headers() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "Subject: Missing From Header\n\nThis email is missing the From header.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Missing From Header".to_string()));
        assert_eq!(parsed_email.from, None);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_eml_unicode_content() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Unicode Test 🚀\nTo: <EMAIL>\n\nHello! This email contains unicode: 你好 🌟 café";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Unicode Test 🚀".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("你好"));
            assert!(body.contains("🌟"));
            assert!(body.contains("café"));
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_excludes_binary_attachments() -> Result<(), Box<dyn std::error::Error>> {
        // Create a multipart email with text content and binary attachment
        let eml_content = r#"From: <EMAIL>
Subject: Email with Binary Attachment
To: <EMAIL>
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain; charset="utf-8"

This is the actual email text content that should be extracted.

--boundary123
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="binary_file.bin"
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU8
--boundary123
Content-Type: image/jpeg
Content-Disposition: attachment; filename="image.jpg"

BINARY_IMAGE_DATA_HERE_SHOULD_BE_EXCLUDED
--boundary123--"#;

        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Email with Binary Attachment".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Verify that only text content is extracted
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("This is the actual email text content"));
            // Verify binary content is NOT included
            assert!(!body.contains("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJ"));
            assert!(!body.contains("BINARY_IMAGE_DATA_HERE_SHOULD_BE_EXCLUDED"));
            assert!(!body.contains("base64"));
        } else {
            panic!("Expected cleaned_plain_text_body to contain text content");
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_greek_characters() -> Result<(), Box<dyn std::error::Error>> {
        // Create an email with Greek characters in various encodings
        let eml_content = r#"From: <EMAIL>
Subject: =?UTF-8?B?zp/Ouc66zr/Ovc6/zrzOuc66z4wgzrXOuc+CzrHOuc+EzrfPgc65zrE=?=
To: <EMAIL>
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: 8bit

Γεια σας! Αυτό είναι ένα email με ελληνικούς χαρακτήρες.

Περιεχόμενο:
• Κείμενο στα ελληνικά
• Ειδικοί χαρακτήρες: άλφα, βήτα, γάμμα
• Τόνοι και διαλυτικά: ά, έ, ή, ί, ό, ύ, ώ, ΐ, ΰ

Τέλος μηνύματος."#;

        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;

        // Check that the subject is properly decoded
        assert!(parsed_email.subject.is_some());
        let subject = parsed_email.subject.as_ref().unwrap();
        assert!(subject.contains("Οικονομικό"), "Subject should contain Greek text: {}", subject);

        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Verify that Greek characters are preserved in the body
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("Γεια σας"), "Body should contain 'Γεια σας': {}", body);
            assert!(body.contains("ελληνικούς χαρακτήρες"), "Body should contain 'ελληνικούς χαρακτήρες': {}", body);
            assert!(body.contains("άλφα, βήτα, γάμμα"), "Body should contain Greek letters: {}", body);
            assert!(body.contains("ά, έ, ή, ί, ό, ύ, ώ"), "Body should contain accented characters: {}", body);
            assert!(body.contains("Τέλος μηνύματος"), "Body should contain 'Τέλος μηνύματος': {}", body);
        } else {
            panic!("Expected cleaned_plain_text_body to contain Greek text");
        }
        Ok(())
    }

    #[test]
    fn test_parse_mbox_greek_characters() -> Result<(), Box<dyn std::error::Error>> {
        // Create an mbox file with Greek characters
        let mbox_content = r#"From <EMAIL> Mon Jan  1 10:00:00 2024
From: <EMAIL>
Subject: =?UTF-8?B?zp/Ouc66zr/Ovc6/zrzOuc66z4wgzrXOuc+CzrHOuc+EzrfPgc65zrE=?=
To: <EMAIL>
Content-Type: text/plain; charset="utf-8"

Γεια σας! Αυτό είναι ένα email με ελληνικούς χαρακτήρες στο mbox format.

"#;

        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert!(email.subject.is_some());

        // Verify that Greek characters are preserved in the body
        if let Some(body) = &email.cleaned_plain_text_body {
            assert!(body.contains("Γεια σας"), "Body should contain 'Γεια σας': {}", body);
            assert!(body.contains("ελληνικούς χαρακτήρες"), "Body should contain 'ελληνικούς χαρακτήρες': {}", body);
        } else {
            panic!("Expected cleaned_plain_text_body to contain Greek text");
        }
        Ok(())
    }

    #[test]
    fn test_parse_invalid_file_path() {
        let result = parse_eml("non_existent_file.eml");
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_empty_file() -> Result<(), Box<dyn std::error::Error>> {
        let mut file = NamedTempFile::new()?;
        // Write empty content
        file.write_all(b"")?;
        let file_path = file.path().to_str().unwrap();

        let result = parse_eml(file_path);
        // Should handle empty files gracefully
        assert!(result.is_ok() || result.is_err()); // Either outcome is acceptable
        Ok(())
    }
}
