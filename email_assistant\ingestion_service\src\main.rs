// AI-Assisted Email Response System - Ingestion Service Main
// Command-line interface for the email ingestion service

use ingestion_service::*;
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("AI Email Assistant - Ingestion Service");

    let args: Vec<String> = std::env::args().collect();

    // Check if this is a test-embedding command (doesn't need database)
    if args.len() > 1 && args[1] == "test-embedding" {
        let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
            .unwrap_or_else(|_| "http://localhost:8003".to_string());

        println!("Testing embedding service connection...");
        match get_embedding("This is a test email for embedding generation.", &embedding_service_url).await {
            Ok(embedding) => {
                println!("✓ Embedding service working! Generated embedding with {} dimensions", embedding.len());
                println!("  First 5 values: {:?}", &embedding[..5.min(embedding.len())]);
            }
            Err(e) => {
                eprintln!("✗ Embedding service test failed: {}", e);
                eprintln!("  Make sure the embedding service is running on {}", embedding_service_url);
            }
        }
        return Ok(());
    }

    // Establish Qdrant connection for other commands
    let client = match establish_connection().await {
        Ok(client) => {
            println!("✓ Qdrant connection established");
            client
        }
        Err(e) => {
            eprintln!("⚠ Qdrant connection failed: {}", e);
            eprintln!("  Make sure Qdrant is running on http://localhost:6334");
            return Err(e.into());
        }
    };

    // Setup collections
    if let Err(e) = setup_collections(&client).await {
        eprintln!("Warning: Collection setup failed: {}", e);
    } else {
        println!("✓ Qdrant collections ready");
    }
    
    // Process command line arguments
    if args.len() > 1 {
        match args[1].as_str() {
            "ingest" => {
                if args.len() < 3 {
                    eprintln!("Usage: {} ingest <file_or_directory_path> [--with-embeddings]", args[0]);
                    return Ok(());
                }

                let path = &args[2];
                let with_embeddings = args.len() > 3 && args[3] == "--with-embeddings";

                if with_embeddings {
                    let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
                        .unwrap_or_else(|_| "http://localhost:8003".to_string());

                    if fs::metadata(path)?.is_dir() {
                        // For directory, process each file individually with embeddings
                        for entry in fs::read_dir(path)? {
                            let entry = entry?;
                            let file_path = entry.path();

                            if let Some(extension) = file_path.extension() {
                                if extension == "eml" {
                                    if let Some(path_str) = file_path.to_str() {
                                        match ingest_email_file_with_embeddings(&client, path_str, &embedding_service_url).await {
                                            Ok(msg_id) => {
                                                println!("✓ Processed {} - Message: {}", path_str, msg_id);
                                            }
                                            Err(e) => eprintln!("Warning: Failed to process {}: {}", path_str, e),
                                        }
                                    }
                                } else if extension == "mbox" {
                                    if let Some(path_str) = file_path.to_str() {
                                        match ingest_mbox_file_with_embeddings(&client, path_str, &embedding_service_url).await {
                                            Ok(results) => {
                                                println!("✓ Processed mbox {} - {} emails", path_str, results.len());
                                            }
                                            Err(e) => eprintln!("Warning: Failed to process {}: {}", path_str, e),
                                        }
                                    }
                                }
                            }
                        }
                    } else if path.ends_with(".mbox") {
                        ingest_mbox_file_with_embeddings(&client, path, &embedding_service_url).await?;
                    } else {
                        ingest_email_file_with_embeddings(&client, path, &embedding_service_url).await?;
                    }
                } else {
                    eprintln!("Note: Qdrant integration requires embeddings. Use --with-embeddings flag.");
                    eprintln!("Example: cargo run -- ingest {} --with-embeddings", path);
                }
            }
            "list" => {
                let messages = get_recent_messages(&client, Some(10)).await?;
                println!("Recent messages:");
                for message in messages {
                    println!("  {} - {} ({})",
                        message.id,
                        message.subject.unwrap_or_else(|| "No subject".to_string()),
                        message.from_address.unwrap_or_else(|| "Unknown sender".to_string())
                    );
                }
            }
            "search" => {
                if args.len() < 3 {
                    eprintln!("Usage: {} search <query_text>", args[0]);
                    return Ok(());
                }

                let query_text = &args[2];
                let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
                    .unwrap_or_else(|_| "http://localhost:8003".to_string());

                println!("Searching for: {}", query_text);
                let query_embedding = get_embedding(query_text, &embedding_service_url).await?;
                let results = search_similar_messages(&client, query_embedding, Some(5), Some(0.5)).await?;

                println!("Found {} similar messages:", results.len());
                for result in results {
                    println!("  Score: {:.3} - {} - {} ({})",
                        result.score,
                        result.message.id,
                        result.message.subject.unwrap_or_else(|| "No subject".to_string()),
                        result.message.from_address.unwrap_or_else(|| "Unknown sender".to_string())
                    );
                }
            }
            "test" => {
                println!("✓ Email parsing functionality ready!");
                println!("✓ Qdrant connection working");
                println!("✓ Embedding service integration ready");
                println!("✓ All systems operational");
            }

            _ => {
                println!("Available commands:");
                println!("  ingest <file_or_directory> --with-embeddings  - Ingest emails with vector embeddings");
                println!("  list                                           - List recent messages");
                println!("  search <query_text>                            - Search for similar emails");
                println!("  test                                           - Test system functionality");
                println!("  test-embedding                                 - Test embedding service connection");
                println!("");
                println!("Examples:");
                println!("  cargo run -- ingest sample.eml --with-embeddings");
                println!("  cargo run -- ingest emails_directory --with-embeddings");
                println!("  cargo run -- search \"project update meeting\"");
                println!("  cargo run -- list");
            }
        }
    } else {
        println!("✓ Email parsing functionality ready!");
        println!("✓ Database setup complete");
        println!("Use 'cargo run -- help' for available commands");
    }
    
    Ok(())
}
