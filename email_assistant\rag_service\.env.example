# RAG Service Configuration

# Embedding Model Configuration
EMBEDDING_MODEL=intfloat/e5-large-v2

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=mistral:7b

# Ingestion Service Configuration
INGESTION_SERVICE_URL=http://localhost:8080

# Service Configuration
RAG_SERVICE_HOST=0.0.0.0
RAG_SERVICE_PORT=8003

# Logging Configuration
LOG_LEVEL=INFO

# Compliance Logging Configuration (EU AI Act & GDPR)
COMPLIANCE_LOG_DIR=logs
COMPLIANCE_LOG_LEVEL=INFO
COMPLIANCE_LOG_MAX_SIZE=104857600
COMPLIANCE_LOG_BACKUP_COUNT=50
COMPLIANCE_LOG_RETENTION_DAYS=200

# Pseudonymisation Configuration (GDPR)
PSEUDONYMISATION_ENABLED=false
PSEUDONYMISATION_LEVEL=disabled
PSEUDONYMISATION_PRESERVE_STRUCTURE=true
PSEUDONYMISATION_DETERMINISTIC=true
PSEUDONYMISATION_SESSION_KEY=

# Custom Pseudonymisation Patterns (optional)
# PSEUDO_PATTERN_EMAIL=custom_email_pattern
# PSEUDO_PATTERN_PHONE=custom_phone_pattern

# Note: Copy this file to .env and adjust settings for your deployment
# For production, consider enabling pseudonymisation for cloud service protection
