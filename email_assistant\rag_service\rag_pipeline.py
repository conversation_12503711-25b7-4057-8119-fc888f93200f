"""
RAG Pipeline for Email Draft Generation

This module implements the Retrieval-Augmented Generation pipeline that:
1. Takes an incoming email as input
2. Generates embeddings for the email content
3. Searches for similar historical emails using vector similarity
4. Composes a prompt with retrieved context
5. Generates a draft response using a local LLM (Ollama/Llama-3)

Compliance Features:
- Comprehensive audit logging for EU AI Act compliance
- GDPR pseudonymisation support for cloud service protection
- Transaction tracking for regulatory reporting
"""

import logging
import httpx
import time
from typing import List, Dict, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain_ollama import OllamaLLM
from langchain_core.output_parsers import StrOutputParser
from sentence_transformers import SentenceTransformer
import numpy as np
from compliance_logger import get_compliance_logger
from pseudonymizer import get_pseudonymizer, pseudonymize_email_content, pseudonymize_retrieved_emails

logger = logging.getLogger(__name__)

class RAGPipeline:
    """RAG Pipeline for generating email draft responses"""
    
    def __init__(
        self,
        ollama_base_url: str = "http://localhost:11434",
        ollama_model: str = "mistral:7b",
        ingestion_service_url: str = "http://localhost:8080",
        embedding_model_name: str = "intfloat/e5-large-v2",
        max_context_emails: int = 5,
        similarity_threshold: float = 0.7
    ):
        """
        Initialize the RAG pipeline
        
        Args:
            ollama_base_url: Base URL for Ollama service
            ollama_model: Name of the Ollama model to use
            ingestion_service_url: URL of the ingestion service for vector search
            embedding_model_name: Name of the sentence transformer model
            max_context_emails: Maximum number of similar emails to retrieve
            similarity_threshold: Minimum similarity score for retrieved emails
        """
        self.ollama_base_url = ollama_base_url
        self.ollama_model = ollama_model
        self.ingestion_service_url = ingestion_service_url
        self.max_context_emails = max_context_emails
        self.similarity_threshold = similarity_threshold
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer(embedding_model_name)
        
        # Initialize LLM
        self.llm = OllamaLLM(
            base_url=ollama_base_url,
            model=ollama_model,
            temperature=0.7
        )
        
        # Initialize HTTP client for ingestion service
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Initialize prompt template
        self.prompt_template = self._create_prompt_template()
        
        # Initialize output parser
        self.output_parser = StrOutputParser()
        
        logger.info(f"RAG Pipeline initialized with model: {ollama_model}")
    
    def _create_prompt_template(self) -> PromptTemplate:
        """Create the RAG prompt template for email draft generation"""
        
        template = """You are an AI assistant helping a legal advisor draft professional email responses. 

Based on the following context of similar historical emails and responses, generate a professional draft response to the incoming email.

CONTEXT - Similar Historical Emails:
{context}

INCOMING EMAIL:
Subject: {subject}
From: {sender}
Content: {content}

INSTRUCTIONS:
1. Generate a professional, courteous response appropriate for a legal advisor
2. Use the context from similar historical emails to inform your response style and content
3. Address the specific points raised in the incoming email
4. Maintain a professional legal tone
5. Include appropriate legal disclaimers if relevant
6. Keep the response concise but comprehensive

DRAFT RESPONSE:"""

        return PromptTemplate(
            input_variables=["context", "subject", "sender", "content"],
            template=template
        )
    
    async def generate_embeddings(self, text: str) -> List[float]:
        """Generate embeddings for the given text"""
        try:
            embeddings = self.embedding_model.encode(text)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise
    
    async def search_similar_emails(
        self, 
        query_embedding: List[float], 
        limit: int = None
    ) -> List[Dict[str, Any]]:
        """Search for similar emails using vector similarity"""
        try:
            limit = limit or self.max_context_emails
            
            # Call the ingestion service's search endpoint
            search_payload = {
                "query_vector": query_embedding,
                "limit": limit,
                "score_threshold": self.similarity_threshold
            }
            
            response = await self.http_client.post(
                f"{self.ingestion_service_url}/search_emails",
                json=search_payload
            )
            response.raise_for_status()
            
            search_results = response.json()
            logger.info(f"Found {len(search_results)} similar emails")
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching similar emails: {e}")
            return []
    
    def _format_context(self, similar_emails: List[Dict[str, Any]]) -> str:
        """Format the retrieved emails into context for the prompt"""
        if not similar_emails:
            return "No similar historical emails found."
        
        context_parts = []
        for i, email in enumerate(similar_emails, 1):
            email_context = f"""
Email {i} (Similarity: {email.get('score', 'N/A'):.3f}):
Subject: {email.get('subject', 'N/A')}
From: {email.get('sender', 'N/A')}
Content: {email.get('content', 'N/A')[:500]}...
Response: {email.get('response', 'No response available')[:300]}...
---"""
            context_parts.append(email_context)
        
        return "\n".join(context_parts)
    
    async def generate_draft(
        self,
        subject: str,
        sender: str,
        content: str
    ) -> Dict[str, Any]:
        """
        Generate a draft email response using RAG with comprehensive compliance logging

        Args:
            subject: Subject of the incoming email
            sender: Sender of the incoming email
            content: Content of the incoming email

        Returns:
            Dictionary containing the generated draft and metadata
        """
        # Initialize compliance logging
        compliance_logger = get_compliance_logger()
        transaction_id = compliance_logger.generate_transaction_id()
        start_time = time.time()

        try:
            logger.info(f"Generating draft for email from {sender} (Transaction: {transaction_id})")

            # Step 1: Log the incoming request
            request_data = {
                "subject": subject,
                "sender": sender,
                "content": content
            }
            compliance_logger.log_draft_request(transaction_id, request_data)

            # Step 2: Apply pseudonymisation if enabled (for potential cloud service calls)
            pseudonymizer = get_pseudonymizer()
            pseudo_subject, pseudo_sender, pseudo_content = pseudonymize_email_content(
                subject, sender, content
            )

            # Step 3: Generate embeddings for the incoming email
            email_text = f"Subject: {subject}\nContent: {content}"
            query_embedding = await self.generate_embeddings(email_text)

            # Log embedding generation
            embedding_info = {
                "dimension": len(query_embedding) if query_embedding else 0,
                "model": self.embedding_model.model_name if hasattr(self.embedding_model, 'model_name') else "unknown"
            }

            # Step 4: Search for similar historical emails
            similar_emails = await self.search_similar_emails(query_embedding)

            # Log context retrieval
            search_metadata = {
                "similarity_threshold": self.similarity_threshold,
                "max_context_emails": self.max_context_emails,
                "search_time_ms": 0  # Could be measured if needed
            }
            compliance_logger.log_context_retrieval(
                transaction_id, embedding_info, similar_emails, search_metadata
            )

            # Step 5: Apply pseudonymisation to retrieved emails if needed
            processed_similar_emails = similar_emails
            if pseudonymizer.config.enabled:
                processed_similar_emails = pseudonymize_retrieved_emails(similar_emails)

            # Step 6: Format context from retrieved emails
            context = self._format_context(processed_similar_emails)

            # Step 7: Compose the prompt (use original data for local LLM)
            prompt = self.prompt_template.format(
                context=context,
                subject=subject,
                sender=sender,
                content=content
            )

            # Log prompt composition
            context_summary = {
                "email_count": len(similar_emails),
                "total_context_length": len(context),
                "pseudonymised": pseudonymizer.config.enabled
            }
            compliance_logger.log_prompt_composition(
                transaction_id, "legal_advisor_rag_v1", context_summary, len(prompt)
            )

            # Step 8: Generate draft using LLM
            logger.info("Calling LLM for draft generation...")
            llm_start_time = time.time()
            draft_response = await self._call_llm(prompt)
            llm_end_time = time.time()

            # Log LLM generation
            llm_metadata = {
                "model": self.ollama_model,
                "base_url": self.ollama_base_url,
                "generation_time_ms": int((llm_end_time - llm_start_time) * 1000)
            }
            generation_result = {
                "draft": draft_response,
                "success": True,
                "error": None
            }
            compliance_logger.log_llm_generation(transaction_id, llm_metadata, generation_result)

            # Step 9: Prepare response
            result = {
                "draft": draft_response,
                "context_emails_count": len(similar_emails),
                "similar_emails": similar_emails,  # Return original emails (not pseudonymised)
                "metadata": {
                    "model": self.ollama_model,
                    "similarity_threshold": self.similarity_threshold,
                    "max_context_emails": self.max_context_emails,
                    "transaction_id": transaction_id,
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
            }

            # Step 10: Log the final response
            processing_metadata = {
                "total_processing_time_ms": int((time.time() - start_time) * 1000),
                "embedding_model": embedding_info.get("model", "unknown"),
                "llm_model": self.ollama_model,
                "pseudonymisation_applied": pseudonymizer.config.enabled,
                "pseudonymisation_stats": pseudonymizer.get_replacement_stats()
            }
            compliance_logger.log_draft_response(transaction_id, result, processing_metadata)

            logger.info(f"Draft generation completed successfully (Transaction: {transaction_id})")
            return result

        except Exception as e:
            logger.error(f"Error generating draft (Transaction: {transaction_id}): {e}")

            # Log the error for compliance
            compliance_logger.log_error(
                transaction_id,
                "draft_generation_error",
                str(e),
                {
                    "subject": subject,
                    "sender": sender,
                    "content_length": len(content),
                    "processing_time_ms": int((time.time() - start_time) * 1000)
                }
            )
            raise
    
    async def _call_llm(self, prompt: str) -> str:
        """Call the LLM to generate the draft response"""
        try:
            # Use LangChain's async invoke method
            response = await self.llm.ainvoke(prompt)
            return response
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            # Fallback response
            return "I apologize, but I'm unable to generate a draft response at this time due to a technical issue. Please try again later."
    
    async def close(self):
        """Close the HTTP client"""
        await self.http_client.aclose()
