# AI Email Assistant - Background Service Startup Script
# This script starts all services as background processes

Write-Host "🚀 Starting AI Email Assistant Services in Background..." -ForegroundColor Green

# Set working directory
$EmailAssistantPath = "C:\Users\<USER>\Desktop\thunder\email_assistant"
Set-Location $EmailAssistantPath

Write-Host "📍 Working directory: $EmailAssistantPath" -ForegroundColor Yellow

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to wait for service to start
function Wait-ForService {
    param([int]$Port, [string]$ServiceName, [int]$TimeoutSeconds = 30)
    
    Write-Host "⏳ Waiting for $ServiceName to start on port $Port..." -ForegroundColor Yellow
    
    $elapsed = 0
    while ($elapsed -lt $TimeoutSeconds) {
        if (Test-Port -Port $Port) {
            Write-Host "✅ $ServiceName is ready on port $Port" -ForegroundColor Green
            return $true
        }
        Start-Sleep -Seconds 2
        $elapsed += 2
    }
    
    Write-Host "❌ $ServiceName failed to start within $TimeoutSeconds seconds" -ForegroundColor Red
    return $false
}

# 1. Start Qdrant Vector Database
Write-Host "🗄️ Starting Qdrant Vector Database..." -ForegroundColor Cyan
$QdrantPath = Join-Path $EmailAssistantPath "ingestion_service\qdrant"
Start-Process -FilePath ".\qdrant.exe" -WorkingDirectory $QdrantPath -WindowStyle Minimized
Wait-ForService -Port 6333 -ServiceName "Qdrant"

# 2. Start RAG Service with Mistral 7B
Write-Host "🧠 Starting RAG Service (Mistral 7B + e5-large-v2)..." -ForegroundColor Cyan
$RagPath = Join-Path $EmailAssistantPath "rag_service"
Start-Process -FilePath "python" -ArgumentList "main.py" -WorkingDirectory $RagPath -WindowStyle Minimized
Wait-ForService -Port 8003 -ServiceName "RAG Service"

# 3. Start Ingestion Service
Write-Host "📥 Starting Ingestion Service..." -ForegroundColor Cyan
$IngestionPath = Join-Path $EmailAssistantPath "ingestion_service"
Start-Process -FilePath "cargo" -ArgumentList "run", "--bin", "ingestion_server" -WorkingDirectory $IngestionPath -WindowStyle Minimized
Wait-ForService -Port 8080 -ServiceName "Ingestion Service"

# 4. Start Desktop App
Write-Host "🖥️ Starting Desktop App..." -ForegroundColor Cyan
$DesktopPath = Join-Path $EmailAssistantPath "desktop_app"
Start-Process -FilePath "npm" -ArgumentList "run", "tauri:dev" -WorkingDirectory $DesktopPath -WindowStyle Normal

Write-Host ""
Write-Host "🎉 All services started successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Service Status:" -ForegroundColor Yellow
Write-Host "  🗄️ Qdrant:           http://localhost:6333 (Dashboard: /dashboard)" -ForegroundColor White
Write-Host "  🧠 RAG Service:      http://localhost:8003 (Mistral 7B)" -ForegroundColor White
Write-Host "  📥 Ingestion:        http://localhost:8080" -ForegroundColor White
Write-Host "  🖥️ Desktop App:      http://localhost:5173" -ForegroundColor White
Write-Host ""
Write-Host "💡 Services are running in background. Close this window safely." -ForegroundColor Green
Write-Host "💡 To stop services, use: .\stop_services_background.ps1" -ForegroundColor Green

# Keep script running to show status
Write-Host ""
Write-Host "Press Ctrl+C to exit this status window (services will continue running)" -ForegroundColor Yellow
try {
    while ($true) {
        Start-Sleep -Seconds 10
        $timestamp = Get-Date -Format "HH:mm:ss"
        Write-Host "[$timestamp] Services running..." -ForegroundColor DarkGray
    }
} catch {
    Write-Host "Exiting status monitor. Services continue running in background." -ForegroundColor Yellow
}
