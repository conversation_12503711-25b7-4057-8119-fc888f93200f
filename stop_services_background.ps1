# AI Email Assistant - Background Service Stop Script
# This script stops all background services

Write-Host "🛑 Stopping AI Email Assistant Background Services..." -ForegroundColor Red

# Function to stop processes by name
function Stop-ServiceByName {
    param([string]$ProcessName, [string]$ServiceName)
    
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "🛑 Stopping $ServiceName..." -ForegroundColor Yellow
            $processes | Stop-Process -Force -ErrorAction SilentlyContinue
            Write-Host "✅ $ServiceName stopped" -ForegroundColor Green
        } else {
            Write-Host "ℹ️ $ServiceName not running" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠️ Error stopping $ServiceName`: $_" -ForegroundColor Yellow
    }
}

# Function to stop processes by port
function Stop-ServiceByPort {
    param([int]$Port, [string]$ServiceName)
    
    try {
        $netstat = netstat -ano | Select-String ":$Port "
        if ($netstat) {
            $pids = $netstat | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
            foreach ($pid in $pids) {
                if ($pid -and $pid -ne "0") {
                    Write-Host "🛑 Stopping $ServiceName (PID: $pid)..." -ForegroundColor Yellow
                    Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
                }
            }
            Write-Host "✅ $ServiceName stopped" -ForegroundColor Green
        } else {
            Write-Host "ℹ️ $ServiceName not running on port $Port" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠️ Error stopping $ServiceName`: $_" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Stopping services..." -ForegroundColor Yellow

# Stop Desktop App (Node.js/Tauri processes)
Stop-ServiceByName -ProcessName "app" -ServiceName "Desktop App (Tauri)"
Stop-ServiceByName -ProcessName "node" -ServiceName "Desktop App (Node.js)"

# Stop Ingestion Service (Rust)
Stop-ServiceByName -ProcessName "ingestion_server" -ServiceName "Ingestion Service"

# Stop RAG Service (Python)
Stop-ServiceByPort -Port 8003 -ServiceName "RAG Service"

# Stop Qdrant
Stop-ServiceByName -ProcessName "qdrant" -ServiceName "Qdrant Database"

# Additional cleanup by port
Write-Host ""
Write-Host "🧹 Additional cleanup by ports..." -ForegroundColor Yellow
Stop-ServiceByPort -Port 6333 -ServiceName "Qdrant (6333)"
Stop-ServiceByPort -Port 8080 -ServiceName "Ingestion Service (8080)"
Stop-ServiceByPort -Port 5173 -ServiceName "Vite Dev Server (5173)"

Write-Host ""
Write-Host "✅ All services stopped successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 You can restart services with: .\start_services_background.ps1" -ForegroundColor Cyan

# Wait a moment before closing
Start-Sleep -Seconds 3
